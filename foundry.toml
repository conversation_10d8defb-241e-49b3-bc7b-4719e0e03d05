[profile.default]
src = "src"
out = "out"
libs = ["lib"]
remappings = [
    "lib/aave-v3-core = aave-v3-core/src",
    "@openzeppelin/contracts=lib/openzeppelin-contracts/contracts",
    "@morpho-blue=lib/morpho-blue/src/",
    "@chainlink=lib/chainlink/contracts/src/v0.8"
]
optimizer = true
optimizer_runs = 200
via_ir = true
fs_permissions = [{ access = "read", path = "./" }]

# RPC endpoints
[rpc_endpoints]
avalanche_fuji = "${AVALANCHE_FUJI_RPC_URL}"
base_sepolia = "${BASE_SEPOLIA_RPC_URL}"

# Etherscan configuration
[etherscan]
avalanche_fuji = { key = "${SNOWTRACE_API_KEY}", url = "https://api.routescan.io/v2/network/testnet/evm/43113/etherscan" }
base_sepolia = { key = "${BASESCAN_API_KEY}", url = "https://api-sepolia.basescan.org/api" }