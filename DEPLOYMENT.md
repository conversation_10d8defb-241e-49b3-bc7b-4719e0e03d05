# Deployment Guide for LendingAPYAggregator

This guide walks you through deploying the complete LendingAPYAggregator system across Avalanche Fuji and Base Sepolia testnets.

## Prerequisites

1. **Install Dependencies**
   ```bash
   make setup
   ```

2. **Set up Environment Variables**
   ```bash
   cp .env.example .env
   ```
   
   Fill in the following required variables in `.env`:
   - `PRIVATE_KEY`: Your deployment wallet private key
   - `SNOWTRACE_API_KEY`: For Avalanche contract verification
   - `BASESCAN_API_KEY`: For Base contract verification
   - `OWNER_ADDRESS`: Address that will own the contracts (optional, defaults to deployer)

3. **Check Environment Setup**
   ```bash
   make check-env
   ```

## Deployment Process

The deployment must be done in a specific order due to cross-chain dependencies:

### Step 1: Deploy to Base Sepolia

Deploy the Morpho CCIP Receiver contract on Base:

```bash
make deploy-base
```

This will deploy:
- `BaseMorphoCCIPReceiver` on Base Sepolia

**Important**: Copy the deployed `BaseMorphoCCIPReceiver` address and update your `.env` file:
```bash
BASE_MORPHO_RECEIVER_ADDRESS=0x...
```

### Step 2: Deploy to Avalanche Fuji

Deploy the main contracts on Avalanche:

```bash
make deploy-avalanche
```

This will deploy:
- `AvalancheMorphoCCIPSender` on Avalanche Fuji
- `LendingAPYAggregator` on Avalanche Fuji

The script will automatically:
- Link the sender to the Base receiver
- Add supported assets (USDC, USDT, WETH, WBTC)
- Set up the aggregator with the sender

### Step 3: Update Cross-Chain References

After both deployments, you need to update the Base receiver with the trusted sender address:

```bash
# Use cast to call the updateTrustedSender function on Base
cast send $BASE_MORPHO_RECEIVER_ADDRESS \
  "updateTrustedSender(address)" $MORPHO_SENDER_ADDRESS \
  --rpc-url $BASE_SEPOLIA_RPC_URL \
  --private-key $PRIVATE_KEY
```

## Contract Addresses

After deployment, update your `.env` file with all deployed addresses:

```bash
# Avalanche Fuji
MORPHO_SENDER_ADDRESS=0x...
LENDING_APY_AGGREGATOR_ADDRESS=0x...

# Base Sepolia  
BASE_MORPHO_RECEIVER_ADDRESS=0x...
```

## Frontend Configuration

1. **Set up Frontend Environment**
   ```bash
   make frontend-setup
   ```

2. **Update Frontend Configuration**
   Edit `ap-yieldz/.env.local`:
   ```bash
   NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id
   NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS=0x...
   ```

3. **Start Frontend Development Server**
   ```bash
   make frontend-dev
   ```

## Testing

### Run Contract Tests
```bash
# All tests
make test

# Unit tests only
make test-unit

# Integration tests only
make test-integration
```

### Test Frontend Integration
1. Connect your wallet to Avalanche Fuji testnet
2. Get testnet tokens from faucets:
   - [Avalanche Fuji Faucet](https://faucet.avax.network/)
   - [Aave Fuji Faucet](https://staging.aave.com/faucet/)
3. Test supply/borrow operations through the UI

## Verification

If automatic verification fails during deployment, you can verify manually:

```bash
# Verify Base contracts
make verify-base

# Verify Avalanche contracts  
make verify-avalanche
```

## Troubleshooting

### Common Issues

1. **"Insufficient fee" error**
   - Ensure you have enough AVAX for CCIP fees when testing cross-chain operations

2. **"UnsupportedAsset" error**
   - Make sure assets are added to the aggregator contract
   - Check that asset addresses are correct for the testnet

3. **Frontend connection issues**
   - Verify contract addresses in frontend config
   - Ensure wallet is connected to the correct network
   - Check that ABIs are up to date

### Getting Testnet Tokens

- **Avalanche Fuji AVAX**: [Avalanche Faucet](https://faucet.avax.network/)
- **Base Sepolia ETH**: [Base Faucet](https://www.coinbase.com/faucets/base-ethereum-goerli-faucet)
- **Test Tokens**: Use Aave's faucet for USDC, USDT, WETH, WBTC on Fuji

## Network Information

### Avalanche Fuji
- Chain ID: 43113
- RPC: https://api.avax-test.network/ext/bc/C/rpc
- Explorer: https://testnet.snowtrace.io/

### Base Sepolia
- Chain ID: 84532
- RPC: https://sepolia.base.org
- Explorer: https://sepolia.basescan.org/

## Next Steps

After successful deployment:
1. Test all functionality through the frontend
2. Monitor cross-chain operations
3. Consider adding more supported assets
4. Implement additional features like automated yield optimization
