# Deployment Configuration
# Private key for deployment (DO NOT commit real private key)
PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000

# RPC URLs
AVALANCHE_FUJI_RPC_URL=https://api.avax-test.network/ext/bc/C/rpc
BASE_SEPOLIA_RPC_URL=https://sepolia.base.org

# Etherscan API Keys for verification
SNOWTRACE_API_KEY=your_snowtrace_api_key_here
BASESCAN_API_KEY=your_basescan_api_key_here

# Contract Addresses (will be populated after deployment)
AAVE_POOL_ADDRESS_FUJI=******************************************
MORPHO_SENDER_ADDRESS=
LENDING_APY_AGGREGATOR_ADDRESS=
BASE_MORPHO_RECEIVER_ADDRESS=

# Chainlink CCIP Configuration
CCIP_ROUTER_AVALANCHE_FUJI=******************************************
CCIP_ROUTER_BASE_SEPOLIA=******************************************
BASE_CHAIN_SELECTOR=10344971235874465080

# Morpho Blue Configuration (Base)
MORPHO_BLUE_ADDRESS_BASE=******************************************

# Owner address for contracts
OWNER_ADDRESS=

# Frontend Environment Variables
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_AVALANCHE_FUJI_RPC=https://api.avax-test.network/ext/bc/C/rpc
NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS=
