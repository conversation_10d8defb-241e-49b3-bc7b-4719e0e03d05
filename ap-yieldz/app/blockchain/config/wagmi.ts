import { getDefaultConfig, getDefaultWallets } from '@rainbow-me/rainbowkit';
import { avalancheFuji } from 'wagmi/chains';
import {
  injectedWallet,
} from "@rainbow-me/rainbowkit/wallets";
import { http, createStorage, cookieStorage } from 'wagmi';

if (!process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID) {
  throw new Error('Missing NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID');
}

const { wallets } = getDefaultWallets();

export const config = getDefaultConfig({
  appName: 'Alligator',
  projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
  wallets: [
    {
      groupName: "Core Wallet",
      wallets: [injectedWallet],
    },
  ],
  chains: [
    avalancheFuji,
  ],
  transports: {
    [avalancheFuji.id]: http(process.env.NEXT_PUBLIC_AVALANCHE_FUJI_RPC || 'https://api.avax-test.network/ext/bc/C/rpc'),
  },
  ssr: true,
  storage: createStorage({
    storage: cookieStorage,
  }),
});

export const LENDING_APY_AGGREGATOR_ADDRESS = process.env.NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS || '0x...';

export const SUPPORTED_TOKENS = {
  avalanche: {
    USDC: '******************************************',
    USDT: '******************************************',
    WETH: '******************************************',
    WBTC: '******************************************',
  },
  base: {
    USDC: '******************************************',
    WETH: '******************************************',
  }
};