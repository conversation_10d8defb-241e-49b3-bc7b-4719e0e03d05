# WalletConnect Project ID - Get one from https://cloud.walletconnect.com/
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id_here

# RPC URLs
NEXT_PUBLIC_AVALANCHE_FUJI_RPC=https://api.avax-test.network/ext/bc/C/rpc
NEXT_PUBLIC_BASE_SEPOLIA_RPC=https://sepolia.base.org

# Contract Addresses (update after deployment)
NEXT_PUBLIC_LENDING_APY_AGGREGATOR_ADDRESS=******************************************
NEXT_PUBLIC_MORPHO_SENDER_ADDRESS=******************************************
NEXT_PUBLIC_BASE_MORPHO_RECEIVER_ADDRESS=******************************************

# API Keys (optional for enhanced features)
NEXT_PUBLIC_ALCHEMY_API_KEY=your_alchemy_api_key_here
NEXT_PUBLIC_INFURA_API_KEY=your_infura_api_key_here

# Development settings
NEXT_PUBLIC_ENABLE_TESTNETS=true
NEXT_PUBLIC_DEBUG_MODE=true