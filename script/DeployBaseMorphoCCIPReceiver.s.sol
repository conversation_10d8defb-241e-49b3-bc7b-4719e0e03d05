// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {<PERSON>ript} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {BaseMorphoCCIPReceiver} from "../src/base/BaseMorphoCCIPReceiver.sol";

contract DeployBaseMorphoCCIPReceiver is Script {
    function run() public {
        // Get configuration from environment variables
        address ccipRouter = vm.envAddress("CCIP_ROUTER_BASE_SEPOLIA");
        address morphoBlue = vm.envAddress("MORPHO_BLUE_ADDRESS_BASE");
        address trustedSender = vm.envOr("MORPHO_SENDER_ADDRESS", address(0));
        
        console.log("Deploying BaseMorphoCCIPReceiver...");
        console.log("CCIP Router:", ccipRouter);
        console.log("Morpho Blue:", morphoBlue);
        
        // Note: trustedSender might be 0 if not deployed yet
        if (trustedSender == address(0)) {
            console.log("Warning: Trusted sender address not set. Will need to be updated after Avalanche deployment.");
        } else {
            console.log("Trusted Sender:", trustedSender);
        }

        vm.startBroadcast();

        BaseMorphoCCIPReceiver receiver = new BaseMorphoCCIPReceiver(
            ccipRouter,
            morphoBlue,
            trustedSender
        );

        console.log("BaseMorphoCCIPReceiver deployed at:", address(receiver));
        console.log("Please update BASE_MORPHO_RECEIVER_ADDRESS in your .env file");

        vm.stopBroadcast();
    }
}
