// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {LendingAPYAggregator} from "../src/LendingAPYAggregator.sol";
import {MarketParams} from "lib/morpho-blue/src/interfaces/IMorpho.sol";

contract DeployLendingAPYAggregator is Script {

    function run() public {
        // Get configuration from environment variables
        address aavePool = vm.envAddress("AAVE_POOL_ADDRESS_FUJI");
        address morphoSender = vm.envAddress("MORPHO_SENDER_ADDRESS");
        address owner = vm.envOr("OWNER_ADDRESS", msg.sender);

        console.log("Deploying LendingAPYAggregator...");
        console.log("Aave Pool:", aavePool);
        console.log("Morpho Sender:", morphoSender);
        console.log("Owner:", owner);

        vm.startBroadcast();

        LendingAPYAggregator aggregator = new LendingAPYAggregator(
            aavePool,
            morphoSender,
            owner
        );

        console.log("LendingAPYAggregator deployed at:", address(aggregator));

        // Add supported assets (Avalanche Fuji testnet addresses)
        address[] memory supportedAssets = new address[](4);
        supportedAssets[0] = ******************************************; // USDC
        supportedAssets[1] = ******************************************; // USDT
        supportedAssets[2] = ******************************************; // WETH
        supportedAssets[3] = ******************************************; // WBTC

        string[] memory assetNames = new string[](4);
        assetNames[0] = "USDC";
        assetNames[1] = "USDT";
        assetNames[2] = "WETH";
        assetNames[3] = "WBTC";

        for (uint i = 0; i < supportedAssets.length; i++) {
            aggregator.addSupportedAsset(supportedAssets[i]);
            console.log("Added supported asset:", assetNames[i], "at", supportedAssets[i]);
        }

        console.log("Please update LENDING_APY_AGGREGATOR_ADDRESS in your .env file");
        console.log("Deployment completed successfully!");

        vm.stopBroadcast();
    }
}
