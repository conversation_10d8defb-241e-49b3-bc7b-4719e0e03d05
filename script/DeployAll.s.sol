// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {Script} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {LendingAPYAggregator} from "../src/LendingAPYAggregator.sol";
import {AvalancheMorphoCCIPSender} from "../src/avalanche/AvalancheMorphoCCIPSender.sol";
import {BaseMorphoCCIPReceiver} from "../src/base/BaseMorphoCCIPReceiver.sol";

/**
 * @title DeployAll
 * @dev Comprehensive deployment script for the entire LendingAPYAggregator system
 * @notice This script should be run in the following order:
 *         1. Deploy on Base first (BaseMorphoCCIPReceiver)
 *         2. Deploy on Avalanche (AvalancheMorphoCCIPSender and LendingAPYAggregator)
 *         3. Update cross-references between contracts
 */
contract DeployAll is Script {
    
    function deployOnBase() public {
        console.log("=== DEPLOYING ON BASE ===");
        
        address ccipRouter = vm.envAddress("CCIP_ROUTER_BASE_SEPOLIA");
        address morphoBlue = vm.envAddress("MORPHO_BLUE_ADDRESS_BASE");
        address trustedSender = address(0); // Will be updated after Avalanche deployment
        
        console.log("CCIP Router (Base):", ccipRouter);
        console.log("Morpho Blue:", morphoBlue);

        vm.startBroadcast();

        BaseMorphoCCIPReceiver receiver = new BaseMorphoCCIPReceiver(
            ccipRouter,
            morphoBlue,
            trustedSender
        );

        console.log("BaseMorphoCCIPReceiver deployed at:", address(receiver));
        console.log("Update BASE_MORPHO_RECEIVER_ADDRESS in .env to:", address(receiver));

        vm.stopBroadcast();
    }

    function deployOnAvalanche() public {
        console.log("=== DEPLOYING ON AVALANCHE ===");
        
        // Get addresses
        address ccipRouter = vm.envAddress("CCIP_ROUTER_AVALANCHE_FUJI");
        address baseReceiver = vm.envAddress("BASE_MORPHO_RECEIVER_ADDRESS");
        uint64 baseChainSelector = uint64(vm.envUint("BASE_CHAIN_SELECTOR"));
        address aavePool = vm.envAddress("AAVE_POOL_ADDRESS_FUJI");
        address owner = vm.envOr("OWNER_ADDRESS", msg.sender);

        console.log("CCIP Router (Avalanche):", ccipRouter);
        console.log("Base Receiver:", baseReceiver);
        console.log("Base Chain Selector:", baseChainSelector);
        console.log("Aave Pool:", aavePool);
        console.log("Owner:", owner);

        vm.startBroadcast();

        // Deploy AvalancheMorphoCCIPSender
        AvalancheMorphoCCIPSender sender = new AvalancheMorphoCCIPSender(
            ccipRouter,
            baseReceiver,
            baseChainSelector
        );

        console.log("AvalancheMorphoCCIPSender deployed at:", address(sender));

        // Deploy LendingAPYAggregator
        LendingAPYAggregator aggregator = new LendingAPYAggregator(
            aavePool,
            address(sender),
            owner
        );

        console.log("LendingAPYAggregator deployed at:", address(aggregator));

        // Add supported assets
        _addSupportedAssets(aggregator);

        console.log("Update MORPHO_SENDER_ADDRESS in .env to:", address(sender));
        console.log("Update LENDING_APY_AGGREGATOR_ADDRESS in .env to:", address(aggregator));

        vm.stopBroadcast();
    }

    function _addSupportedAssets(LendingAPYAggregator aggregator) internal {
        // Avalanche Fuji testnet token addresses
        address[] memory supportedAssets = new address[](4);
        supportedAssets[0] = ******************************************; // USDC
        supportedAssets[1] = ******************************************; // USDT
        supportedAssets[2] = ******************************************; // WETH
        supportedAssets[3] = ******************************************; // WBTC

        string[] memory assetNames = new string[](4);
        assetNames[0] = "USDC";
        assetNames[1] = "USDT";
        assetNames[2] = "WETH";
        assetNames[3] = "WBTC";

        for (uint i = 0; i < supportedAssets.length; i++) {
            aggregator.addSupportedAsset(supportedAssets[i]);
            console.log("Added supported asset:", assetNames[i], "at", supportedAssets[i]);
        }
    }

    function run() public {
        console.log("=== COMPREHENSIVE DEPLOYMENT SCRIPT ===");
        console.log("This script will deploy all contracts for the LendingAPYAggregator system");
        console.log("Make sure you have set up your .env file with the correct addresses");
        console.log("");
        console.log("To deploy step by step:");
        console.log("1. forge script script/DeployAll.s.sol:DeployAll --sig 'deployOnBase()' --rpc-url $BASE_SEPOLIA_RPC_URL --broadcast");
        console.log("2. Update BASE_MORPHO_RECEIVER_ADDRESS in .env");
        console.log("3. forge script script/DeployAll.s.sol:DeployAll --sig 'deployOnAvalanche()' --rpc-url $AVALANCHE_FUJI_RPC_URL --broadcast");
        console.log("");
        console.log("Or run this script to see the deployment plan without executing");
    }
}
