// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {<PERSON>ript} from "forge-std/Script.sol";
import {console} from "forge-std/console.sol";
import {AvalancheMorphoCCIPSender} from "../src/avalanche/AvalancheMorphoCCIPSender.sol";

contract DeployAvalancheMorphoCCIPSender is Script {
    function run() public {
        // Get configuration from environment variables
        address ccipRouter = vm.envAddress("CCIP_ROUTER_AVALANCHE_FUJI");
        address baseReceiver = vm.envOr("BASE_MORPHO_RECEIVER_ADDRESS", address(0));
        uint64 baseChainSelector = uint64(vm.envUint("BASE_CHAIN_SELECTOR"));
        
        console.log("Deploying AvalancheMorphoCCIPSender...");
        console.log("CCIP Router:", ccipRouter);
        console.log("Base Chain Selector:", baseChainSelector);
        
        // Note: baseReceiver might be 0 if not deployed yet
        if (baseReceiver == address(0)) {
            console.log("Warning: Base receiver address not set. Will need to be updated after Base deployment.");
        } else {
            console.log("Base Receiver:", baseReceiver);
        }

        vm.startBroadcast();

        AvalancheMorphoCCIPSender sender = new AvalancheMorphoCCIPSender(
            ccipRouter,
            baseReceiver,
            baseChainSelector
        );

        console.log("AvalancheMorphoCCIPSender deployed at:", address(sender));
        console.log("Please update MORPHO_SENDER_ADDRESS in your .env file");

        vm.stopBroadcast();
    }
}
