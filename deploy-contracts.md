# Contract Deployment Guide

Since the Foundry dependencies are taking time to download, here's a manual deployment guide using alternative methods.

## Option 1: Using Remix IDE (Recommended for Quick Deployment)

### Step 1: Deploy BaseMorphoCCIPReceiver on Base Sepolia

1. Go to [Remix IDE](https://remix.ethereum.org/)
2. Create a new file `BaseMorphoCCIPReceiver.sol` and copy the contract from `src/base/BaseMorphoCCIPReceiver.sol`
3. Compile the contract
4. Connect to Base Sepolia testnet (Chain ID: 84532)
5. Deploy with constructor parameters:
   - `_ccipRouter`: `******************************************`
   - `_morpho`: `******************************************`
   - `_trustedSender`: `******************************************` (will update later)

### Step 2: Deploy AvalancheMorphoCCIPSender on Avalanche Fuji

1. Create `AvalancheMorphoCCIPSender.sol` in Remix
2. Copy contract from `src/avalanche/AvalancheMorphoCCIPSender.sol`
3. Connect to Avalanche Fuji testnet (Chain ID: 43113)
4. Deploy with constructor parameters:
   - `_ccipRouter`: `******************************************`
   - `_baseReceiver`: `[ADDRESS_FROM_STEP_1]`
   - `_baseChainSelector`: `10344971235874465080`

### Step 3: Deploy LendingAPYAggregator on Avalanche Fuji

1. Create `LendingAPYAggregator.sol` in Remix
2. Copy contract from `src/LendingAPYAggregator.sol`
3. Deploy with constructor parameters:
   - `_aavePool`: `******************************************`
   - `_morphoSender`: `[ADDRESS_FROM_STEP_2]`
   - `_owner`: `[YOUR_WALLET_ADDRESS]`

### Step 4: Configure Contracts

1. Call `addSupportedAsset` on LendingAPYAggregator for each token:
   - USDC: `******************************************`
   - USDT: `******************************************`
   - WETH: `******************************************`
   - WBTC: `******************************************`

2. Update the trusted sender on BaseMorphoCCIPReceiver:
   - Call `updateTrustedSender([MORPHO_SENDER_ADDRESS])`

## Option 2: Using Hardhat (Alternative)

If you prefer Hardhat, create deployment scripts in a `hardhat` folder with the same contract addresses.

## Contract Addresses (Update after deployment)

```
# Avalanche Fuji
MORPHO_SENDER_ADDRESS=0x...
LENDING_APY_AGGREGATOR_ADDRESS=0x...

# Base Sepolia
BASE_MORPHO_RECEIVER_ADDRESS=0x...
```

## Network Information

### Avalanche Fuji Testnet
- Chain ID: 43113
- RPC: https://api.avax-test.network/ext/bc/C/rpc
- Explorer: https://testnet.snowtrace.io/
- Faucet: https://faucet.avax.network/

### Base Sepolia Testnet
- Chain ID: 84532
- RPC: https://sepolia.base.org
- Explorer: https://sepolia.basescan.org/
- Faucet: https://www.coinbase.com/faucets/base-ethereum-goerli-faucet

## Next Steps

After deployment:
1. Update the frontend configuration with deployed addresses
2. Test the frontend integration
3. Verify contracts on block explorers
