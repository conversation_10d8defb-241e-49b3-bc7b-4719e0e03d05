# Load environment variables
include .env
export

# Default target
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  setup           - Install dependencies and set up the project"
	@echo "  test            - Run all tests"
	@echo "  test-unit       - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  deploy-base     - Deploy contracts to Base testnet"
	@echo "  deploy-avalanche - Deploy contracts to Avalanche Fuji testnet"
	@echo "  deploy-all      - Deploy all contracts (Base first, then Avalanche)"
	@echo "  verify-base     - Verify contracts on Base"
	@echo "  verify-avalanche - Verify contracts on Avalanche"
	@echo "  frontend-setup  - Set up frontend environment"
	@echo "  frontend-dev    - Start frontend development server"
	@echo "  clean           - Clean build artifacts"

# Setup
.PHONY: setup
setup:
	@echo "Setting up the project..."
	forge install
	cd ap-yieldz && npm install

# Testing
.PHONY: test
test:
	@echo "Running all tests..."
	forge test -vvv

.PHONY: test-unit
test-unit:
	@echo "Running unit tests..."
	forge test --match-path "test/unit/*" -vvv

.PHONY: test-integration
test-integration:
	@echo "Running integration tests..."
	forge test --match-path "test/intergrations/*" -vvv

# Deployment
.PHONY: deploy-base
deploy-base:
	@echo "Deploying to Base testnet..."
	@if [ -z "$(BASE_SEPOLIA_RPC_URL)" ]; then echo "BASE_SEPOLIA_RPC_URL not set"; exit 1; fi
	@if [ -z "$(PRIVATE_KEY)" ]; then echo "PRIVATE_KEY not set"; exit 1; fi
	forge script script/DeployAll.s.sol:DeployAll --sig "deployOnBase()" \
		--rpc-url $(BASE_SEPOLIA_RPC_URL) \
		--private-key $(PRIVATE_KEY) \
		--broadcast \
		--verify \
		--etherscan-api-key $(BASESCAN_API_KEY)

.PHONY: deploy-avalanche
deploy-avalanche:
	@echo "Deploying to Avalanche Fuji testnet..."
	@if [ -z "$(AVALANCHE_FUJI_RPC_URL)" ]; then echo "AVALANCHE_FUJI_RPC_URL not set"; exit 1; fi
	@if [ -z "$(PRIVATE_KEY)" ]; then echo "PRIVATE_KEY not set"; exit 1; fi
	@if [ -z "$(BASE_MORPHO_RECEIVER_ADDRESS)" ]; then echo "BASE_MORPHO_RECEIVER_ADDRESS not set. Deploy to Base first."; exit 1; fi
	forge script script/DeployAll.s.sol:DeployAll --sig "deployOnAvalanche()" \
		--rpc-url $(AVALANCHE_FUJI_RPC_URL) \
		--private-key $(PRIVATE_KEY) \
		--broadcast \
		--verify \
		--etherscan-api-key $(SNOWTRACE_API_KEY)

.PHONY: deploy-all
deploy-all: deploy-base
	@echo "Waiting 30 seconds for Base deployment to settle..."
	@sleep 30
	@echo "Please update BASE_MORPHO_RECEIVER_ADDRESS in .env file with the deployed address"
	@echo "Then run: make deploy-avalanche"

# Verification (if deployment without verification fails)
.PHONY: verify-base
verify-base:
	@echo "Verifying Base contracts..."
	@if [ -z "$(BASE_MORPHO_RECEIVER_ADDRESS)" ]; then echo "BASE_MORPHO_RECEIVER_ADDRESS not set"; exit 1; fi
	forge verify-contract $(BASE_MORPHO_RECEIVER_ADDRESS) \
		src/base/BaseMorphoCCIPReceiver.sol:BaseMorphoCCIPReceiver \
		--chain-id 84532 \
		--etherscan-api-key $(BASESCAN_API_KEY) \
		--constructor-args $(shell cast abi-encode "constructor(address,address,address)" $(CCIP_ROUTER_BASE_SEPOLIA) $(MORPHO_BLUE_ADDRESS_BASE) $(MORPHO_SENDER_ADDRESS))

.PHONY: verify-avalanche
verify-avalanche:
	@echo "Verifying Avalanche contracts..."
	@if [ -z "$(LENDING_APY_AGGREGATOR_ADDRESS)" ]; then echo "LENDING_APY_AGGREGATOR_ADDRESS not set"; exit 1; fi
	@if [ -z "$(MORPHO_SENDER_ADDRESS)" ]; then echo "MORPHO_SENDER_ADDRESS not set"; exit 1; fi
	forge verify-contract $(LENDING_APY_AGGREGATOR_ADDRESS) \
		src/LendingAPYAggregator.sol:LendingAPYAggregator \
		--chain-id 43113 \
		--etherscan-api-key $(SNOWTRACE_API_KEY) \
		--constructor-args $(shell cast abi-encode "constructor(address,address,address)" $(AAVE_POOL_ADDRESS_FUJI) $(MORPHO_SENDER_ADDRESS) $(OWNER_ADDRESS))

# Frontend
.PHONY: frontend-setup
frontend-setup:
	@echo "Setting up frontend environment..."
	cd ap-yieldz && cp .env.example .env.local
	@echo "Please update ap-yieldz/.env.local with your deployed contract addresses"

.PHONY: frontend-dev
frontend-dev:
	@echo "Starting frontend development server..."
	cd ap-yieldz && npm run dev

# Cleanup
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	forge clean
	cd ap-yieldz && rm -rf .next node_modules/.cache

# Development helpers
.PHONY: check-env
check-env:
	@echo "Checking environment variables..."
	@echo "PRIVATE_KEY: $(if $(PRIVATE_KEY),✓ Set,✗ Not set)"
	@echo "AVALANCHE_FUJI_RPC_URL: $(if $(AVALANCHE_FUJI_RPC_URL),✓ Set,✗ Not set)"
	@echo "BASE_SEPOLIA_RPC_URL: $(if $(BASE_SEPOLIA_RPC_URL),✓ Set,✗ Not set)"
	@echo "AAVE_POOL_ADDRESS_FUJI: $(if $(AAVE_POOL_ADDRESS_FUJI),✓ Set,✗ Not set)"
	@echo "MORPHO_BLUE_ADDRESS_BASE: $(if $(MORPHO_BLUE_ADDRESS_BASE),✓ Set,✗ Not set)"
	@echo "BASE_MORPHO_RECEIVER_ADDRESS: $(if $(BASE_MORPHO_RECEIVER_ADDRESS),✓ Set,✗ Not set)"
	@echo "LENDING_APY_AGGREGATOR_ADDRESS: $(if $(LENDING_APY_AGGREGATOR_ADDRESS),✓ Set,✗ Not set)"

.PHONY: local-node
local-node:
	@echo "Starting local Anvil node..."
	anvil --host 0.0.0.0 --port 8545
